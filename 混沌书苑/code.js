window=global
const JSEncrypt = require('crypto-js');
function encrypt() {
    let _0x520b8d = new JSEncrypt();
    _0x520b8d['setPublicKey']('-----BEGIN PUBLIC KEY-----MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChOdMggWiQCf1eEEpm5d+3iWHC3/w+lHQbMqHVEftwoRixm4Sd1iZjRdaeHJd0bClIHMCzzTQKO9bdiz+PdjuZwlTZEYCV6zzgT5Q9fPpbjtvYSK8XyzNSpjhDmNyLeuBoS+JNkdLzHoJisLuNZpVKlhh0d022/hLfd1FRnS+QtwIDAQAB-----END PUBLIC KEY-----');
    return _0x520b8d['encrypt']('654321(||::||)www.chaoslib.com(||::||)1755102275738'
    )['trim']();
}
console.log(encrypt())